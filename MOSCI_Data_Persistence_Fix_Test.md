# MOSCI Data Persistence During Validation - Fix Test Plan

## Problem Fixed
**Issue**: When users clicked Save and received validation errors due to incomplete data, the incomplete inmate records were being cleared from the `model.Inmates` collection instead of being preserved for the user to complete.

**Root Cause**: 
1. In JSON parsing logic: OffenderIds were being cleared when FirstName/LastName were empty
2. In validation logic: OffenderIds were being cleared when offender data was not found
3. This caused incomplete records to disappear from the grid on subsequent save attempts

## Solution Implemented
1. **Preserved User Input Data**: Removed logic that clears OffenderId during JSON parsing and validation
2. **Enhanced Validation**: Moved offender existence check into validation logic without clearing user data
3. **Improved Error Messages**: Added specific error messages for invalid offender IDs vs missing fields
4. **Data Persistence**: All user-entered data (including incomplete) is now preserved between save attempts

## Changes Made

### 1. JSON Parsing Logic Fix
**File:** `MVC/Areas/Transfers/Controllers/MosciController.cs`
**Lines:** 275-277

**Before:**
```csharp
//Check for correct offender ids
if (string.IsNullOrWhiteSpace(inmate.FirstName) || string.IsNullOrWhiteSpace(inmate.LastName))
{
    inmate.OffenderId = "";
    model.Inmates.Add(inmate);
}
else
{
    model.Inmates.Add(inmate);
}
```

**After:**
```csharp
// Always add the inmate to preserve user input data
// Don't clear OffenderId here - let validation handle it later
model.Inmates.Add(inmate);
```

### 2. Validation Logic Enhancement
**File:** `MVC/Areas/Transfers/Controllers/MosciController.cs`
**Lines:** 543-591

**Key Changes:**
- Removed logic that cleared OffenderId when offender data was null
- Added offender existence check to validation criteria
- Added specific error message collection for different validation failures
- Preserved all user input data regardless of validation state

### 3. Error Message Improvement
**File:** `MVC/Areas/Transfers/Controllers/MosciController.cs`
**Lines:** 593-609

**Enhancement:**
- Shows specific error messages when available (invalid offender ID, same institutions, etc.)
- Falls back to generic message for basic field validation
- Provides clear guidance on what needs to be fixed

## Test Cases

### Test Case 1: Invalid Offender ID Persistence
**Steps:**
1. Navigate to MOSCI screen
2. Enter invalid offender ID (e.g., "A999999")
3. Fill in some other fields (leave some empty)
4. Click Save button
5. Observe error message
6. Click Save button again

**Expected Results:**
- First save: Shows specific error about invalid offender ID
- Second save: Same error, all user data still visible in grid
- User can correct the offender ID and continue editing

### Test Case 2: Incomplete Data Persistence
**Steps:**
1. Navigate to MOSCI screen
2. Enter valid offender ID (e.g., "A123456")
3. Auto-populate fills some fields
4. Leave Schedule Date empty
5. Click Save button
6. Click Save button again

**Expected Results:**
- First save: Shows "Incomplete data in row 1" error
- Second save: Same error, all data including offender ID still visible
- User can fill in missing Schedule Date and save successfully

### Test Case 3: Mixed Valid and Invalid Data
**Steps:**
1. Navigate to MOSCI screen
2. Add 3 inmates:
   - Row 1: Valid offender ID, complete data
   - Row 2: Invalid offender ID, incomplete data
   - Row 3: Valid offender ID, missing Schedule Date
3. Click Save button multiple times

**Expected Results:**
- Shows specific errors for rows 2 and 3
- All data persists between save attempts
- Row 1 data remains intact
- User can fix issues incrementally

### Test Case 4: From/To Institution Conflict
**Steps:**
1. Navigate to MOSCI screen
2. Enter valid offender ID
3. Set From and To institutions to the same value
4. Fill all other required fields
5. Click Save button
6. Click Save button again

**Expected Results:**
- Shows "From and To institutions cannot be the same" error
- All data persists including the conflicting institution selections
- User can change To institution and save successfully

## Verification Points

### Data Persistence Verification
- [ ] All OffenderIds remain visible after validation failures
- [ ] FirstName and LastName fields retain user input
- [ ] Institution selections are preserved
- [ ] Schedule dates remain as entered
- [ ] Description text is maintained

### Error Message Verification
- [ ] Invalid offender ID shows specific error message
- [ ] Missing required fields show appropriate guidance
- [ ] Institution conflicts are clearly identified
- [ ] Multiple errors are listed comprehensively

### User Experience Verification
- [ ] Users can continue editing after validation failures
- [ ] No data loss occurs during save attempts
- [ ] Progressive completion of fields is supported
- [ ] Save succeeds when all issues are resolved

## Browser Console Monitoring
During testing, monitor browser console for:
- No JavaScript errors during save operations
- Proper JSON data collection and transmission
- Correct server response handling
- Grid state maintenance after validation failures

## Success Criteria
✅ **Fix is successful when:**
1. Users never lose their entered data during validation failures
2. Specific error messages guide users to fix issues
3. Progressive completion of incomplete records is supported
4. Save operation works correctly once all validation passes
5. No regression in existing functionality

## Summary of Changes Made

### Files Modified:
1. **MVC/Areas/Transfers/Controllers/MosciController.cs**
   - Lines 275-277: Removed OffenderId clearing in JSON parsing
   - Lines 543-591: Enhanced validation logic to preserve user data
   - Lines 593-609: Improved error message handling

### Key Improvements:
1. **Data Preservation**: All user input is now preserved between save attempts
2. **Better Validation**: Offender existence check moved to validation without data loss
3. **Clearer Errors**: Specific messages for different validation failures
4. **User Experience**: Progressive editing support for incomplete records

### Testing Recommendations:
- Test with various combinations of valid/invalid offender IDs
- Verify data persistence across multiple save attempts
- Confirm error messages are helpful and specific
- Ensure successful saves work after fixing validation issues
